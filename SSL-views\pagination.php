<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">
</head>

<body>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct" crossorigin="anonymous"></script>
</body>
<style>
    .active::after {
        content: none;
    }
</style>

</html>


<?php

if (!isset($_GET['page'])) {
    $page_no = 1;
} else {
    $page_no = $_GET['page'];
}
// echo $page_no;
$results_per_page = 10;
$previous_page = $page_no - 1;
$next_page = $page_no + 1;
$adjacents = "2";
// Determine the value of isApproved based on the URL route
$isApproved = 0;
$current_route = basename($_SERVER['REQUEST_URI']);

// echo $current_route;

if (strpos($current_route, 'student-reject') !== false  || strpos($current_route, 'staff-rejected') !== false) {
    $isApproved = 2;
} elseif (strpos($current_route, 'student-approved') !== false || strpos($current_route, 'staff-approved') !== false) {
    $isApproved = 1;
} elseif (strpos($current_route, 'student-pending') !== false || strpos($current_route, 'staff-pending') !== false) {
    $isApproved = 0;
}

// Construct the query based on the determined value of isApproved
if (strpos($current_route, 'staff-rejected') !== false || strpos($current_route, 'staff-approved') !== false || strpos($current_route, 'staff-pending') !== false) {

    $query = "SELECT *, @ab:=@ab+1 AS SrNo FROM staff_user, (SELECT @ab:= 0)
            AS ab JOIN data_uploads 
            WHERE staff_user.staffId = data_uploads.enrollNo  AND isApproved =$isApproved";
} else if (strpos($current_route, 'student-reject') !== false || strpos($current_route, 'student-approved') !== false || strpos($current_route, 'student-pending') !== false) {
    $query = "SELECT *, @ab:=@ab+1 AS SrNo FROM student_user, (SELECT @ab:= 0)
             AS ab  JOIN data_uploads 
             WHERE student_user.enrollNo = data_uploads.enrollNo  AND isApproved = $isApproved";
}

$result = mysqli_query($conn, $query);

$number_of_result = mysqli_num_rows($result);

$total_no_of_pages = ceil($number_of_result / $results_per_page);

$second_last = $total_no_of_pages - 1;


$offset = ($page_no - 1) * $results_per_page;
// echo $offset;
// if ($page_no > 1) {
//     echo "<li class='page-item'><a class='page-link' href='?page=1'>First Page</a></li>";
// }
?>

<?php if (empty($searchKeyword)) : ?>
    <nav>
        <ul class="pagination pagination-sm">
            <?php
            if ($page_no > 1) {
                echo "<li class='page-item'><a class='page-link' href='?page=1'>First Page</a></li>";
            }
            ?>

            <li class="page-item" <?php if ($page_no <= 1) {
                                        echo "class='disabled'";
                                    } ?>>
                <a class="page-link" <?php if ($page_no > 1) {
                                            echo "href='?page=$previous_page'";
                                        } ?>>Previous</a>
            </li>

            <?php
            if ($total_no_of_pages <= 10) {
                for ($counter = 1; $counter <= $total_no_of_pages; $counter++) {
                    if ($counter == $page_no) {
                        echo "<li class='active page-item'><a class='page-link'>$counter</a></li>";
                    } else {
                        echo "<li class='page-item'><a class='page-link' href='?page=$counter'>$counter</a></li>";
                    }
                }
            } elseif ($total_no_of_pages > 10) {

                if ($page_no <= 4) {
                    for ($counter = 1; $counter < 8; $counter++) {
                        if ($counter == $page_no) {
                            echo "<li class='page-item active'><a class='page-link'>$counter</a></li>";
                        } else {
                            echo "<li class='page-item'><a class='page-link' href='?page=$counter'>$counter</a></li>";
                        }
                    }
                    echo "<li><a class='page-link'>...</a></li>";
                    echo "<li><a class='page-link' href='?page=$second_last'>$second_last</a></li>";
                    echo "<li><a class='page-link' href='?page=$total_no_of_pages'>$total_no_of_pages</a></li>";
                } elseif ($page_no > 4 && $page_no < $total_no_of_pages - 4) {
                    echo "<li><a class='page-link' href='?page=1'>1</a></li>";
                    echo "<li><a class='page-link' href='?page=2'>2</a></li>";
                    echo "<li><a class='page-link'>...</a></li>";
                    for ($counter = $page_no - $adjacents; $counter <= $page_no + $adjacents; $counter++) {
                        if ($counter == $page_no) {
                            echo "<li class='page-item active'><a class='page-link'>$counter</a></li>";
                        } else {
                            echo "<li><a class='page-link' href='?page=$counter'>$counter</a></li>";
                        }
                    }
                    echo "<li><a class='page-link'>...</a></li>";
                    echo "<li><a class='page-link' href='?page=$second_last'>$second_last</a></li>";
                    echo "<li><a class='page-link' href='?page=$total_no_of_pages'>$total_no_of_pages</a></li>";
                } else {
                    echo "<li><a class='page-link' href='?page=1'>1</a></li>";
                    echo "<li><a class='page-link' href='?page=2'>2</a></li>";
                    echo "<li><a class='page-link'>...</a></li>";

                    for ($counter = $total_no_of_pages - 6; $counter <= $total_no_of_pages; $counter++) {
                        if ($counter == $page_no) {
                            echo "<li class='page-item active'><a class='page-link'>$counter</a></li>";
                        } else {
                            echo "<li class='page-item'><a class='page-link' href='?page=$counter'>$counter</a></li>";
                        }
                    }
                }
            }
            ?>

            <li class="page-item" <?php if ($page_no >= $total_no_of_pages) {
                                        echo "class='disabled'";
                                    } ?>>
                <a class="page-link" <?php if ($page_no < $total_no_of_pages) {
                                            echo "href='?page=$next_page'";
                                        } ?>>Next</a>
            </li>
            <?php if ($page_no < $total_no_of_pages) {
                echo "<li class='page-item'><a class='page-link' href='?page=$total_no_of_pages'>Last &rsaquo;&rsaquo;</a></li>";
            } ?>
        </ul>
    </nav>
<?php endif; ?>