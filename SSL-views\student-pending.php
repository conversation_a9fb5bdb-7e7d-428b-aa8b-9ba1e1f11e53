<?php

// turn on error reporting
// error_reporting(1);
// ini_set('error_reporting', E_ALL);

// start session
session_start();

if ($_SESSION["uid"] == null) {

  header("location:HOME");
}

// debug session
// var_dump($_SESSION);
// echo '<br>';
// echo $_SESSION["meter_no"];

//The below codes just prints the session values
// echo '<br>';
// print_r($_SESSION);
// echo '<br>';
// echo("{$_SESSION['meter_no']}"."<br />");

$searchKeyword = isset($_GET['search']) ? $_GET['search'] : '';
?>

<html>
<title>Applicants Pending Approval</title>
<div class="topnav">
  <a href="../AHOME">Home</a>
  <a href="student-approved.php">Approved</a>
  <a href="student-rejected.php">Rejected</a>
  <a class="active" href="student-pending.php">Pending</a>
  <a style="float:right" href="../SIGNOUT">Logout</a>
</div>

<head>

  <link rel="stylesheet" type="text/css" href="../NAVIGATION">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" type="text/css" href="../MODAL">
  <link rel="stylesheet" type="text/css" href="../COLLAPSIBLE">


</head>

<body> <!--to align the entire content in centre-->

  <div class="center-image">
    <img src="../LOGO" alt="Varsity Logo">
    <h2 align="center">Pending Student Applicants List</h2>
  </div>


  <div class="search-form col">
    <form method="GET" action="<?php echo $_SERVER['PHP_SELF']; ?>">
      <label class="form-label" for="search">Search students:</label>
      <input class="form-control col-lg-3 mb-3" type="text" id="search" name="search" placeholder="enroll no., email, phone" value="<?php echo htmlspecialchars($searchKeyword, ENT_QUOTES); ?>">
      <input class="btn btn-primary " type="submit" value="Search">
      <input class="btn btn-secondary " type="button" value="Reset" onclick="location.replace(location.pathname);">
    </form>
  </div>
  <!-- <div align="right" style="margin-right: 10px">
      <a href="printBillList.php">Print Pdf</a>
    </div> -->

  <div class='applicants-table'>
    <table class=" bordered-table">
      <tr>
        <th>Sl/No</th>

        <th>Campus</th>
        <th>University ID No</th>
        <th>Name</th>
        <th>Department</th>
        <th>Course</th>
        <th>Sem</th>
        <th>Father</th>
        <th>Gender</th>
        <th>Email</th>
        <th>Phone</th>
        <th>Year of Completion</th>
        <th>ID Front</th>
        <th>ID Back</th>
        <th>Photo</th>
        <th colspan="2">Action</th>

      </tr>

      <?php

      /* To connect to db */
      include("../config/dbConfig.php");
      include("../SSL-views/pagination.php");


      /*    echo $_SESSION["meter_no"];
    echo '<br>';*/
      //$meter_no= $_SESSION["meter_no"];
      // echo '<br>';


      // $sql = "SELECT *, ROW_NUMBER() OVER (ORDER BY id DESC) AS SrNo FROM `student_user` JOIN data_uploads WHERE 
      // student_user.enrollNo = data_uploads.enrollNo  AND isApproved =0";

      // $sql="  SELECT *, (@cnt := IF(@cnt IS NULL, 0,  @cnt) + 1) AS SrNo FROM `student_user` JOIN data_uploads WHERE 
      // student_user.enrollNo = data_uploads.enrollNo  AND isApproved =0   ";

      // $sql="  SELECT *, ROW_NUMBER() OVER(ORDER BY (SELECT 1)) AS SrNo FROM `student_user` JOIN data_uploads WHERE 
      // student_user.enrollNo = data_uploads.enrollNo  AND isApproved =0   "; //Commented on 20-11-2021

      $sql = "SELECT *, @ab:=@ab+1 AS SrNo FROM student_user, (SELECT @ab:= 0)
    AS ab  JOIN data_uploads 
    WHERE student_user.enrollNo = data_uploads.enrollNo  AND isApproved =0";

      if (!empty($searchKeyword)) {
        $sql .= " AND (
                    firstName LIKE '%$searchKeyword%'
                    OR lastName LIKE '%$searchKeyword%'
                    OR email LIKE '%$searchKeyword%'
                    OR mobNo LIKE '%$searchKeyword%'
                )";
      }

      $sql .= " order by SrNo DESC LIMIT " . $offset . ',' . $results_per_page;

      // $sql= "SELECT * FROM student_user";

      $result = $conn->query($sql);

      if ($result !== false && $result->num_rows > 0)


      /* ($result->num_rows > 0)*/ {
        // output data of each row
        while ($row = $result->fetch_assoc()) {
          echo "<tr>
            <td>" . $row["SrNo"] . "</td>

            <td>" . $row["campus"] . "</td>
            <td>" . $row["enrollNo"] . "</td>
            <td>" . $row["firstName"] . ' ' . $row["lastName"] . "</td>
            <td>" . $row["department"] . "</td>
            <td>" . $row["courseName"] . "</td>
            <td>" . $row["semester"] . "</td>
            <td>" . $row["fatherName"] . "</td>
            <td>" . $row["gender"] . "</td>
            <td>" . $row["email"] . "</td>
            <td>" . $row["mobNo"] . "</td>
            <td>" . $row["yearOfCompletion"] . "</td>
            
            <td><a id='IdFront-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['IdFront'] . "'>View</a>
            <td><a id='IdBack-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['IdBack'] . "'>View</a>
            <td><a id='Photo-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['Photo'] . "'>View</a>
            <td><a href = '../STU-APPROVE?enrollNo=$row[enrollNo]'><img class='actionBtn' src='../TICK'></td>
            <td> 
            <button class='collapsible'><img class='actionBtn' src='../CROSS'></button>
            <div class='content'>  
            <form action='../REJECT' method='POST' enctype='multipart/form-data'>            
                <input class='small-inputbox' name='remarks' placeholder='Remarks' required/> 
                <input type='hidden' name='enrollNo' value='$row[enrollNo]'>
                <br><br>
                <input class='small-inputbox' type='submit' name='submit' value='Reject' /> 
                </form>
            </div>
            </td>     
      </tr>";
        }
        echo "</table>";
      } else {
        echo "0 results";
      }
      $conn->close();



      ?><div style='padding: 10px 20px 0px; border-top: dotted 1px #CCC; text-align: right;'>
        <strong>Page <?php echo $page_no . " of " . $total_no_of_pages; ?></strong>
      </div>
    </table>

  </div>

  <?php

  //display the link of the pages in URL  
  // for ($page_no = 1; $page_no <= $total_no_of_pages; $page_no++) {
  //   echo '<a href = "student-pending.php?page=' . $page_no . '">' . $page_no . ' </a>';
  // }
  ?>

  <!-- The Modal -->
  <div id="myModal" class="modal">
    <span class="close">&times;</span>
    <img class="modal-content" id="img01">
    <div id="caption"></div>
  </div>




</body>

</html>

</body>
<script text="text/javascript" src="../js/modal.js"></script>

<!-- Below script is for collapsible -->
<script>
  var coll = document.getElementsByClassName("collapsible");
  var i;

  for (i = 0; i < coll.length; i++) {
    coll[i].addEventListener("click", function() {
      this.classList.toggle("active");
      var content = this.nextElementSibling;
      if (content.style.maxHeight) {
        content.style.maxHeight = null;
      } else {
        content.style.maxHeight = content.scrollHeight + "px";
      }
    });
  }
</script>
<!-- Above script for collapsible ends -->

</html>