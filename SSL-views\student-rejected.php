<?php
// turn on error reporting
// error_reporting(1);
// ini_set('error_reporting', E_ALL);

// start session
session_start();

if ($_SESSION["uid"] == null) {
  header("location:../HOME");
}

// debug session
// var_dump($_SESSION);
// echo '<br>';
// echo $_SESSION["meter_no"];

//The below codes just prints the session values
// echo '<br>';
// print_r($_SESSION);
// echo '<br>';
// echo("{$_SESSION['meter_no']}"."<br />");

$searchKeyword = isset($_GET['search']) ? $_GET['search'] : '';
?>

<html>
<title>Rejected Applicants</title>
<div class="topnav">
  <a href="../AHOME">Home</a>
  <a href="student-approved.php">Approved</a>
  <a class="active" href="student-rejected.php">Rejected</a>
  <a href="student-pending.php">Pending</a>
  <a style="float:right" href="../SIGNOUT">Logout</a>
</div>

<head>
  <link rel="stylesheet" type="text/css" href="../NAVIGATION">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" type="text/css" href="../MODAL">
</head>

<body>
  <!--to align the entire content in centre-->
  <div class="center-image">
    <img src="../LOGO" alt="Varsity Logo">
    <h2 align="center">Rejected Student Applicants List</h2>

    <!-- Search Form -->

  </div>
  <div class="search-form col">
    <form method="GET" action="<?php echo $_SERVER['PHP_SELF']; ?>">
      <label class="form-label" for="search">Search students:</label>
      <input class="form-control col-lg-3 mb-3" type="text" id="search" name="search" placeholder="enroll no., email, phone" value="<?php echo htmlspecialchars($searchKeyword, ENT_QUOTES); ?>">
      <input class="btn btn-primary " type="submit" value="Search">
      <input class="btn btn-secondary " type="button" value="Reset" onclick="location.replace(location.pathname);">
    </form>
  </div>

  <div class='applicants-table col'>
    <table>
      <tr>
        <th>Sl/No</th>
        <th>Campus</th>
        <th>University ID No</th>
        <th>Name</th>
        <th>Department</th>
        <th>Course</th>
        <th>Sem</th>
        <th>Father</th>
        <th>Gender</th>
        <th>Email</th>
        <th>Phone</th>
        <th>Completion Year</th>
        <th>Remark</th>
        <th>ID Front</th>
        <th>ID Back</th>
        <th>Photo</th>
        <th colspan="2">Action</th>
      </tr>
      <?php
      /* To connect to db */
      include("../config/dbConfig.php");
      include("../SSL-views/pagination.php");

      $sql = "SELECT *, @ab:=@ab+1 AS SrNo FROM student_user
                    JOIN data_uploads ON student_user.enrollNo = data_uploads.enrollNo
                    WHERE isApproved = 2";

      if (!empty($searchKeyword)) {
        $sql .= " AND (
                    firstName LIKE '%$searchKeyword%'
                    OR lastName LIKE '%$searchKeyword%'
                    OR email LIKE '%$searchKeyword%'
                    OR mobNo LIKE '%$searchKeyword%'
                )";
      }

      $sql .= " ORDER BY SrNo DESC
                LIMIT $offset, $results_per_page";

      $result = $conn->query($sql);

      if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
          echo "<tr>
                        <td>" . $row["SrNo"] . "</td>
                        <td>" . $row["campus"] . "</td>
                        <td>" . $row["enrollNo"] . "</td>
                        <td>" . $row["firstName"] . ' ' . $row["lastName"] . "</td>
                        <td>" . $row["department"] . "</td>
                        <td>" . $row["courseName"] . "</td>
                        <td>" . $row["semester"] . "</td>
                        <td>" . $row["fatherName"] . "</td>
                        <td>" . $row["gender"] . "</td>
                        <td>" . $row["email"] . "</td>
                        <td>" . $row["mobNo"] . "</td>
                        <td>" . $row["yearOfCompletion"] . "</td>
                        <td>" . $row["remarks"] . "</td>
                        <td><a id='IdFront-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['IdFront'] . "'>View</a>
                        <td><a id='IdBack-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['IdBack'] . "'>View</a>
                        <td><a id='Photo-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['Photo'] . "'>View</a>
                        <td><a href='../STU-APPROVE?enrollNo=$row[enrollNo]'><img class='actionBtn' src='../TICK'></td>
                    </tr>";
        }
        echo "</table>";
      } else {
        if (empty($searchKeyword)) {
          echo "No rejected applications found.";
        } else {
          echo "No rejected applications found matching the search criteria.";
        }
      }
      $conn->close();
      ?>
    </table>
    <div style='padding: 10px 20px 0px; border-top: dotted 1px #CCC; text-align: right;'>
      <strong>Page <?php echo $page_no . " of " . $total_no_of_pages; ?></strong>
    </div>
  </div>

  <?php

  //display the link of the pages in URL  
  // for ($page = 1; $page <= $number_of_page; $page++) {
  //   echo '<a href = "student-rejected.php?page=' . $page . '">' . $page . ' </a>';
  // }
  // 
  ?>

  <!-- The Modal -->
  <div id="myModal" class="modal">
    <span class="close">&times;</span>
    <img class="modal-content" id="img01">
    <div id="caption"></div>
  </div>
</body>
<script text="text/javascript" src="../js/modal.js"></script>

</html>