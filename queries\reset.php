<?php
include '../config/dbConfig.php';

// turn on error reporting
error_reporting(1);
ini_set('error_reporting', E_ALL);

// start session
session_start();
if ($_SESSION["uid"] == null){

    header("location:../AHOME");
  }

// debug session
//var_dump($_SESSION);

$UserID=$_SESSION["uid"];
$OldPassword=$_POST["currentpwd"];
$NewPassword=$_POST["newpwd"];
$cnfNewPassword=$_POST["cnewpwd"];


if($cnfNewPassword !== $NewPassword){

    echo   '<script type="text/javascript"> 
    alert("Passwords do not match.. Try again!!!"); 
    window.location.href = "PWDRESET";
    </script>';

}else
        if(isset($_POST["pwdReset"]))
        {
            $sql="SELECT pwd FROM admin_table WHERE uid='$UserID';";
            $result = mysqli_query($conn, $sql);  //mysql_query($qry);
            $row = mysqli_fetch_array($result);

                $CheckOldPassword= $row['pwd'];

                //  echo "<script>alert('$CheckOldPassword !!!')</script>";
                //  echo "$CheckOldPassword";   
                if( $CheckOldPassword !== $OldPassword){
                    echo   '<script type="text/javascript"> 
                    alert("Wrong Current Password .. Try again!!!"); 
                    window.location.href = "PWDRESET";
                    </script>';
                }else{

                   $sql="UPDATE admin_table
                            SET pwd = '$NewPassword'
                            WHERE uid='$UserID';";

                                if($conn->query($sql)===TRUE)
                                {
                                    echo   '<script type="text/javascript"> 
                                    alert("Password Reset Sucessful"); 
                                    window.location.href = "SIGNOUT";
                                    </script>';
                                }
                                else
                                {
                                    echo"Error:" . $sql . "<br>" . $conn->error;
                                }

                  
                }
            

            
        }


else{
    die("failed: ".$conn->connect_error);
    echo 'Failed to Update Password';  
  }
   
?>