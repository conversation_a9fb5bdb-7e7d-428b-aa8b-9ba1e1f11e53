body{

    margin-top: 30px;
    margin-left: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
}

/* Style for Navigation bar starts here */

    /* Add a black background color to the top navigation */
.topnav {
  background-color: #333;
  overflow: hidden;
  position: relative;
  top: 0;
  /* left: 1;
  right: 1; */
  width: 100%; 
}

/* Style the links inside the navigation bar */
.topnav a {
  float: left;
  color: #f2f2f2;
  text-align: center;
  padding: 10px 10px;
  text-decoration: none;
  font-size: 17px;
}

/* Change the color of links on hover */
.topnav a:hover {
  background-color: #ddd;
  color: black;
}

/* Add a color to the active/current link */
.topnav a.active {
  background-color: #4b5195;
  color: white;
}
      
/* Style for Navigation bar ends here */


/* Style for Footer */
    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        background-color: dimgray;
        color: white;
        color: white;
        padding-top: 2px;
        padding-bottom: 2px;
       text-align: center;
  margin-top: 30px;
  font-weight: 100;
  clear: both;
  font-family: unset;
      }
      
/* Style for Footer ends here */

/* Style for Footer */
.footer1 {
  
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: dimgray;
  color: white;
  padding-top: 2px;
  padding-bottom: 2px;
  text-align: center;
  margin-top: 10px;
  font-weight: 100;
  clear: both;
}

/* Style for Footer ends here */
      
.center-image{
  display:flex;
  justify-content:center; /* horizontally center */
  align-items:center;    /* vertically center */
  height:80px;
  /* width:300px; */
  margin-bottom: 5px;
  /* margin-top: 45px; */
  background:whitesmoke;
}

.center-image img{
 max-width:50%;
 max-height:50%;
}



    
/* CSS Code for Table displayed */
   
table {
  /* position: relative; */
  /* display: inline-block; */
border-collapse: collapse;
border: 1px solid black;
width: 100%;
color:#333;
/* color: #588c7e; */
/* font-family: monospace; */
font-size: 12px;
text-align: left;
}
table, td, th {
  padding-left: 10px;
  border: 1px solid black;
}
th {
background-color: #4b5195;
color: white;
border: 1px solid black;
height: 50px;
text-align: center;
}
tr:nth-child(even) {
  background-color: #f2f2f2;
  border: 1px solid black;
}

/* CSS Code for Table displayed ends here */



.container{
    width: 90%;
    margin-left: 20px;
    margin-right: 20px;
    margin-bottom: 50px;
    margin-top: 1px;
    padding: 20px;
}

label {
    width:250px;
    display: inline-block;
  }
  input{
    width:200px;
    display: inline-block;
  }

  button{
    display:inline-block;     /*Typically a button wouldn't need its own line*/
    margin:0 auto;
    width: 300px; 
    height: 25px;
  }

  .center-content{
    display:flex;
    justify-content:center; /* horizontally center */
    align-items:center;    /* vertically center */
  }

  .center-container{
    padding: 100px;
    vertical-align: middle;
    align-content: center;
  }

  .applicants-table{
    padding: 5px;
    align-content: center;
  }

  .redText{
    display: inline-block;  
    /* color: rgb(236, 13, 13); */
  }

  p{
    display: inline-block;
  }

  .radio{
    width: 10px;
  }

  .btn-primary{
    display: flex;
    justify-content: center;
    align-items: center;   
  }

  .table_image{
    padding: 2px 2px 2px 2px;
    box-align:center;
    width: 100px;
    height: 100px; 
    /* width:100%;
    max-width:200px" */
  }


.less-space{
  width:auto;
  display: inline-block;
  padding-right: 10px;
}

.login-btn{
  width:100%;
}

      /* For two div in one line starts */
#block_container {
  /* display: flex; */
width: 100%;

}
#bloc1 {
  float: left
}
#bloc2 {
  float: right
}
      /* For two div in one line Ends */
      

/* For Action Button Pics starts  */
      .actionBtn{
        padding: 2px 2px 2px 2px;
        box-align:center;
        width: 25px;
        height: 25px; 
      }
 /* For Action Button Pics ends */
 
/* for reject action button inside collapsible starts  */
 .small-inputbox{
  width: 100px;
  height: 30px; 
  padding:2px;
}
/* for reject action button inside collapsible ends  */
      