/*
//--------------------------------- Single Modal view code starts-------------------------------------

// Get the modal----------
var modal = document.getElementById("myModal");

// Get the image and insert it inside the modal - use its "alt" text as a caption----------
var img = document.getElementById("myImg");
var img1 = document.getElementById("myImg1");
var img2 = document.getElementById("myImg2");

var modalImg = document.getElementById("img01");
var captionText = document.getElementById("caption");

img.onclick = function(){
  modal.style.display = "block";
  modalImg.src = this.src;
  captionText.innerHTML = this.alt;
}

window.onclick=function(event){
  if (event.target == modal) {
    modal.style.display ="none";
  } else {
    
  }
}

img1.onclick = function(){
  modal.style.display = "block";
  modalImg.src = this.src;
  captionText.innerHTML = this.alt;
}

img2.onclick = function(){
  modal.style.display = "block";
  modalImg.src = this.src;
  captionText.innerHTML = this.alt;
}

// Get the <span> element that closes the modal----------
var span = document.getElementsByClassName("close")[0];

// When the user clicks on <span> (x), close the modal----------
span.onclick = function() {
  modal.style.display = "none";
}

//--------------------------------- Single Modal view code ends-------------------------------------
*/


//--------------------------------- Multi Modal view code starts-------------------------------------

                        // Get the modal
                        var modal = document.getElementById("myModal");

                        // Get the image and insert it inside the modal - use its "alt" text as a caption
                        // var img = document.getElementById("myImg");
                        var modalImg = document.getElementById("img01");
                        var captionText = document.getElementById("caption");
        
                        function myFunc(el){
                            //var ImgSrc =el.src;
                            var ImgSrc = el.dataset.src;
                            
                            var altText =el.alt;
                            modal.style.display = "block";
                            modalImg.src = ImgSrc;
                            captionText.innerHTML = altText;
                        }
                        window.onclick=function(event){
                        if (event.target == modal) {
                            modal.style.display ="none";
                        } else {
                            
                        }
                        }
        
                        // Get the <span> element that closes the modal
                        var span = document.getElementsByClassName("close")[0];
        
                        // When the user clicks on <span> (x), close the modal
                        span.onclick = function() {
                        modal.style.display = "none";
                        }

//--------------------------------- Multi Modal view code ends-------------------------------------
        