<?php

// turn on error reporting
error_reporting(1);
ini_set('error_reporting', E_ALL);

// start session
session_start();

// echo $_SESSION["uid"];

if(isset($_COOKIE['uid'])):
  setcookie('uid', '', time()-7000000, '/');
endif;

if ($_SESSION["uid"] == null){

  header("location:./home");
}
//debug session
// var_dump($_SESSION);
// echo '<br>';
// echo $_SESSION["uid"];

//The below codes just prints the session values
// echo '<br>';
// print_r($_SESSION);
// echo '<br>';
// echo("{$_SESSION['uid']}"."<br />");

?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="shortcut icon" href="FAVICON">
    <link rel="stylesheet" type="text/css" href="NAVIGATION">
      
    <title>Reset Password</title>
</head>
<body>

    <!-- Navigation bar for navstyle2.css -->
    <div class="topnav">
 <a class="active" href="AHOME">Home</a>
  <!-- <a href="#news">News</a>
  <a href="#contact">Contact</a>
  <a href="#about">About</a> -->
  <a style="float:right" href="SIGNOUT">Logout</a>
</div>
 <!-- Navigation bar for navstyle2.css ends here -->
           
        <div class=center-image>
            <img src="LOGO" alt="Varsity Logo">  
            <h2 align="center">NKN-Internet Access Registered Applicants</h2>
          </div>

    <div class="wrapper" align='center'>
        
        <form class="form-signin" action="RESET" method="POST">       
          <h3 align='center' class="form-signin-heading">Reset Password</h3>
          <label for="">Enter Current Password</label>  
            <br>
          <input type="text" class="form-control" name="currentpwd" placeholder="Current Password" required autofocus="" />
            <br> <br>
          <label for="">New Password</label> 

            <br>
          <input type="password" class="form-control" name="newpwd" placeholder="New Password" required/>     
            <br>  <br>
          <label for="">Confirm New Password</label> 
            <br>
          <input type="text" class="form-control" name="cnewpwd" placeholder="Confirm Password" required/>
            <br> <br>

          <!-- <label class="checkbox">
            <input type="checkbox" value="remember-me" id="rememberMe" name="rememberMe"> Check to Confirm to reset the password
          </label> -->
          <button class="btn btn-lg btn-primary btn-block" type="pwdReset" value="pwdReset" name="pwdReset">Reset</button>   
        </form>
      </div>


          <div class="footer">
          <p>&copy Computer Centre, Assam University</p>
          </div>
    </body>
</html>